#!/bin/bash

# Test script to verify syntax
echo "Testing script syntax..."

# Test the setup script syntax
echo "Checking setup-laravel.sh syntax..."
bash -n setup-laravel.sh
if [ $? -eq 0 ]; then
    echo "✅ setup-laravel.sh syntax is correct"
else
    echo "❌ setup-laravel.sh has syntax errors"
fi

# Test the configure script syntax
echo "Checking configure-laravel-ec2.sh syntax..."
bash -n configure-laravel-ec2.sh
if [ $? -eq 0 ]; then
    echo "✅ configure-laravel-ec2.sh syntax is correct"
else
    echo "❌ configure-laravel-ec2.sh has syntax errors"
fi

# Test the update script syntax
echo "Checking update-laravel-project.sh syntax..."
bash -n update-laravel-project.sh
if [ $? -eq 0 ]; then
    echo "✅ update-laravel-project.sh syntax is correct"
else
    echo "❌ update-laravel-project.sh has syntax errors"
fi

echo "Syntax check complete!"
