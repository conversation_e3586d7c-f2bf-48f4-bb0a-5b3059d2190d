#!/bin/bash

# Simple Laravel EC2 Setup Script
# This script automates the setup of <PERSON><PERSON> on EC2 with GitHub integration

# Configuration variables
GITHUB_REPO="https://github.com/SocheatSorng/LaravelReact.git"
PROJECT_NAME="LaravelReact"
WEB_ROOT="/var/www/html"
PROJECT_PATH="$WEB_ROOT/$PROJECT_NAME"
LARAVEL_PATH="$PROJECT_PATH/backend"

echo "=========================================="
echo "Laravel EC2 Setup Script"
echo "=========================================="

# Update system packages
echo "[INFO] Updating system packages..."
sudo apt-get update -y
sudo apt-get upgrade -y
echo "[SUCCESS] System packages updated"

# Install Apache Web Server
echo "[INFO] Installing Apache web server..."
sudo apt-get install -y apache2

# Start and enable Apache
echo "[INFO] Starting and enabling Apache service..."
sudo systemctl start apache2
sudo systemctl enable apache2

# Check Apache status
if sudo systemctl is-active --quiet apache2; then
    echo "[SUCCESS] Apache is running"
else
    echo "[ERROR] Apache failed to start"
    sudo systemctl status apache2
    exit 1
fi

# Install PHP and required modules
echo "[INFO] Installing PHP and Apache PHP module..."
sudo apt-get install -y php libapache2-mod-php php-mysql php-cli php-fpm php-mbstring php-zip php-gd php-json php-curl php-xml php-bcmath php-tokenizer

# Install MySQL
echo "[INFO] Installing MySQL server..."
sudo apt-get install -y mysql-server
sudo systemctl start mysql
sudo systemctl enable mysql
echo "[SUCCESS] MySQL server installed and started"

# Install additional tools
echo "[INFO] Installing additional tools..."
sudo apt-get install -y git unzip curl vim rsync
echo "[SUCCESS] Additional tools installed"

# Install Composer
echo "[INFO] Installing Composer..."
if ! command -v composer >/dev/null 2>&1; then
    curl -sS https://getcomposer.org/installer | php
    sudo mv composer.phar /usr/local/bin/composer
    sudo chmod +x /usr/local/bin/composer
    echo "[SUCCESS] Composer installed"
else
    echo "[WARNING] Composer already installed"
fi

# Configure Apache
echo "[INFO] Configuring Apache web server..."

# Enable required Apache modules
echo "[INFO] Enabling Apache modules..."
sudo a2enmod rewrite
sudo a2enmod ssl
sudo a2enmod headers
sudo a2enmod expires
sudo a2enmod php7.4 2>/dev/null || sudo a2enmod php8.0 2>/dev/null || sudo a2enmod php8.1 2>/dev/null || sudo a2enmod php8.2 2>/dev/null || echo "[WARNING] Could not determine PHP version module"
echo "[SUCCESS] Apache modules enabled"

# Test Apache configuration
echo "[INFO] Testing Apache configuration..."
if sudo apache2ctl configtest; then
    echo "[SUCCESS] Apache configuration is valid"
else
    echo "[ERROR] Apache configuration has errors"
    exit 1
fi

# Create backup directory
echo "[INFO] Creating backup directory..."
sudo mkdir -p ~/Backup

# Backup default Apache configuration
echo "[INFO] Backing up default Apache configuration..."
sudo cp /etc/apache2/sites-available/000-default.conf ~/Backup/000-default.conf.original

# Create Laravel-optimized Apache configuration
echo "[INFO] Creating Laravel-optimized Apache configuration..."
sudo tee /etc/apache2/sites-available/000-default.conf > /dev/null << 'EOL'
<VirtualHost *:80>
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/html/LaravelReact/backend/public

    <Directory /var/www/html/LaravelReact/backend/public>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted

        # Laravel-specific directives
        RewriteEngine On

        # Handle Angular and Vue.js routes
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>

    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set Content-Security-Policy "default-src 'self'"

    # Hide Apache version
    ServerTokens Prod
    ServerSignature Off

    # Logging
    ErrorLog ${APACHE_LOG_DIR}/laravel_error.log
    CustomLog ${APACHE_LOG_DIR}/laravel_access.log combined

    # Log level
    LogLevel warn
</VirtualHost>
EOL

# Create SSL configuration template
echo "[INFO] Creating SSL configuration template..."
sudo tee /etc/apache2/sites-available/000-default-ssl.conf > /dev/null << 'EOL'
<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/html/LaravelReact/backend/public

    <Directory /var/www/html/LaravelReact/backend/public>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted

        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>

    # SSL Configuration
    SSLEngine on
    # SSLCertificateFile /path/to/your/certificate.crt
    # SSLCertificateKeyFile /path/to/your/private.key
    # SSLCertificateChainFile /path/to/your/chain.crt

    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"

    ErrorLog ${APACHE_LOG_DIR}/laravel_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/laravel_ssl_access.log combined
</VirtualHost>
</IfModule>
EOL

# Test new configuration
echo "[INFO] Testing new Apache configuration..."
if sudo apache2ctl configtest; then
    echo "[SUCCESS] New Apache configuration is valid"
else
    echo "[ERROR] New Apache configuration has errors"
    echo "[INFO] Restoring original configuration..."
    sudo cp ~/Backup/000-default.conf.original /etc/apache2/sites-available/000-default.conf
    exit 1
fi

echo "[SUCCESS] Apache virtual host configured"

# Restart Apache to apply configuration
echo "[INFO] Restarting Apache to apply configuration..."
sudo systemctl restart apache2

# Verify Apache is running
if sudo systemctl is-active --quiet apache2; then
    echo "[SUCCESS] Apache is running successfully"
else
    echo "[ERROR] Apache failed to start"
    sudo systemctl status apache2
fi

# Clone GitHub repository
echo "[INFO] Setting up Laravel project from GitHub..."
cd $WEB_ROOT

if [ -d "$PROJECT_PATH" ]; then
    echo "[WARNING] Project directory exists. Backing up..."
    sudo mv "$PROJECT_PATH" "${PROJECT_PATH}-backup-$(date +%Y%m%d-%H%M%S)"
fi

sudo git clone $GITHUB_REPO $PROJECT_NAME
sudo chown -R www-data:www-data $PROJECT_PATH
sudo chmod -R 755 $PROJECT_PATH

echo "[SUCCESS] Project cloned from GitHub"

# Setup Laravel environment
echo "[INFO] Setting up Laravel environment..."
cd $LARAVEL_PATH

# Install PHP dependencies
echo "[INFO] Installing PHP dependencies..."
sudo -u www-data composer install --no-dev --optimize-autoloader

# Set proper permissions
echo "[INFO] Setting proper permissions..."
sudo chown -R www-data:www-data $LARAVEL_PATH
sudo chmod -R 755 $LARAVEL_PATH
sudo chmod -R 775 $LARAVEL_PATH/storage
sudo chmod -R 775 $LARAVEL_PATH/bootstrap/cache

# Create .env file if it doesn't exist
if [ ! -f "$LARAVEL_PATH/.env" ]; then
    echo "[INFO] Creating .env file..."
    sudo -u www-data cp $LARAVEL_PATH/.env.example $LARAVEL_PATH/.env

    # Generate application key
    sudo -u www-data php artisan key:generate
    echo "[SUCCESS] .env file created and application key generated"
else
    echo "[WARNING] .env file already exists"
fi

# Configure MySQL
echo "[INFO] Configuring MySQL..."
echo "Please run mysql_secure_installation manually after this script completes"

# Create database and user
echo "[INFO] Setting up database..."
read -p "Enter database name (default: laravel_db): " DB_NAME
DB_NAME=${DB_NAME:-laravel_db}

read -p "Enter database username (default: laravel_user): " DB_USER
DB_USER=${DB_USER:-laravel_user}

read -s -p "Enter database password: " DB_PASS
echo

# Create database and user
sudo mysql -e "CREATE DATABASE IF NOT EXISTS $DB_NAME;"
sudo mysql -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';"
sudo mysql -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"

echo "[SUCCESS] Database and user created"

# Update .env file with database configuration
echo "[INFO] Updating .env file with database configuration..."
sudo sed -i "s/DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" $LARAVEL_PATH/.env
sudo sed -i "s/DB_USERNAME=.*/DB_USERNAME=$DB_USER/" $LARAVEL_PATH/.env
sudo sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASS/" $LARAVEL_PATH/.env

echo "[SUCCESS] .env file updated with database configuration"

# Run database migrations
echo "[INFO] Running database migrations..."
sudo -u www-data php artisan migrate --force

# Clear and cache configuration
echo "[INFO] Optimizing Laravel..."
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache

echo "[SUCCESS] Laravel optimized"

# Restart Apache
echo "[INFO] Restarting Apache..."
sudo systemctl restart apache2
sudo systemctl enable apache2

echo "[SUCCESS] Apache restarted and enabled"

# Final status check
echo "[INFO] Performing final status check..."
if sudo systemctl is-active --quiet apache2; then
    echo "[SUCCESS] Apache is running"
else
    echo "[ERROR] Apache is not running"
fi

if sudo systemctl is-active --quiet mysql; then
    echo "[SUCCESS] MySQL is running"
else
    echo "[ERROR] MySQL is not running"
fi

# Display final information
echo
echo "=========================================="
echo "Laravel Setup Complete!"
echo "=========================================="
echo "Apache Status: $(sudo systemctl is-active apache2)"
echo "Apache Version: $(apache2 -v 2>/dev/null | head -n1 || echo 'Version check failed')"
echo "PHP Version: $(php -v 2>/dev/null | head -n1 || echo 'Version check failed')"
echo "MySQL Status: $(sudo systemctl is-active mysql)"
echo
echo "Project Information:"
echo "- Project Path: $LARAVEL_PATH"
echo "- Web Root: $LARAVEL_PATH/public"
echo "- Database: $DB_NAME"
echo "- Database User: $DB_USER"
echo
echo "Configuration files:"
echo "- Apache config: /etc/apache2/sites-available/000-default.conf"
echo "- SSL template: /etc/apache2/sites-available/000-default-ssl.conf"
echo "- Backup: ~/Backup/000-default.conf.original"
echo
echo "Log files:"
echo "- Laravel logs: $LARAVEL_PATH/storage/logs/laravel.log"
echo "- Apache error: /var/log/apache2/laravel_error.log"
echo "- Apache access: /var/log/apache2/laravel_access.log"
echo
echo "Enabled Apache modules:"
sudo a2enmod --list 2>/dev/null | grep -E "(rewrite|ssl|headers|expires|php)" || echo "Module list unavailable"
echo
echo "Next steps:"
echo "1. Run: sudo mysql_secure_installation"
echo "2. Configure your .env file with additional settings"
echo "3. Set up SSL certificate (recommended)"
echo "4. Test your application by visiting your server's IP address"
echo
echo "To update your project from GitHub:"
echo "cd $PROJECT_PATH && git pull origin main"
echo
echo "To view Laravel logs:"
echo "tail -f $LARAVEL_PATH/storage/logs/laravel.log"
echo "=========================================="
