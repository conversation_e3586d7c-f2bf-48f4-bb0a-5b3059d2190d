    1  sudo apt-get update
    2  sudo apt-get upgrade
    3  sudo apt install apache2 php mysql-server phpmyadmin php-mbstring php-zip php-gd php-json php-curl
    4  sudo mysql -u root
    5  sudo mysql -u root -p
    6  sudo mysql
    7  sudo mysql -u root
    8  sudo mysql -u root -p
    9  sudo systemctl restart apache2
   10  dkpg -l | grep phpmyadmin
   11  sudo apt update
   12  sudo apt install phpmyadmin
   13  ls /etc/apache2/conf-available/phpmyadmin.conf
   14  ls /etc/apache2/conf-enabled/phpmyadmin.conf
   15  sudo a2enconf phpmyadmin
   16  sudo apt list phpmyadmin
   17  sudo apt remove phpmyadmin
   18  sudo nano /etc/apache2/conf-available/phpmyadmin.conf
   19  sudo apt remove phpmyadmin
   20  sudo apt install phpmyadmin
   21  sudo systemctl restart apache2
   22  sudo apt list
   23  sudo apt list grep | php
   24  sudo apt list | grep php
   25  sudo ln -s /etc/phpmyadmin/apache.conf /etc/apache2/conf-available/phpmyadmin.conf
   26  sudo a2enconf phpmyadmin.conf
   27  sudo systemctl restart apache2
   28  sudo ln -s /usr/share/phpmyadmin /var/www/html/phpmyadmin
   29  sudo chown -R www-data:www-data /var/www/html/phpmyadmin
   30  sudo chmod -R 755 /var/www/html/phpmyadmin
   31  sudo chown -R www-data:www-data /var/www/html/
   32  sudo apt install composer
   33  ls
   34  cd /home
   35  ls
   36  cd ubuntu/
   37  ls
   38  cd /var/www/html/
   39  sudo apt install composer
   40  which composer
   41  sudo mv $(which composer) /var/www/html/
   42  which composer
   43  composer --version
   44  sudo apt remove --purge composer
   45  sudo apt autoremove --purge
   46  which composer
   47  cd ~
   48  which composer
   49  ls
   50  composer --version
   51  ls /usr/bin/composer
   52  ls /usr/local/bin/composer
   53  ls /etc/composer
   54  ls /var/lib/composer
   55  cd /var/www/html/
   56  sudo apt install composer
   57  composer
   58  git
   59  https://github.com/SocheatSorng/LaravelReact.git
   60  **************:SocheatSorng/LaravelReact.git
   61  cd ~
   62  ls
   63  cd /home
   64  ls
   65  cd ubuntu/
   66  ls
   67  sudo mv backend.zip /var/www/html/
   68  cd /var/www/html/
   69  ls
   70  unzip backend.zip
   71  sudo unzip backend.zip
   72  ls
   73  sudo mv backend ~/
   74  ls
   75  sudo mv backend.zip ~/
   76  cd ~
   77  sudo mv backend/ /var/www/html/
   78  ls
   79  cd /var/www/html/
   80  ls
   81  cd backend/
   82  ls
   83  ls -a
   84  sudo apt install vim
   85  sudo vim .env
   86  sudo chmod -R 777 storage
   87  sudo php artisan migrate
   88  sudo vim .env
   89  sudo php artisan migrate
   90  cd ~
   91  ls
   92  sudo vim /etc/apache2/sites-available/000-default.conf
   93  sudo cp /etc/apache2/sites-available/000-default.conf ~/Backup/
   94  ls
   95  mkdir Backup
   96  sudo cp /etc/apache2/sites-available/000-default.conf Backup/
   97  sudo vim /etc/apache2/sites-available/000-default.conf
   98  sudo a2enmod rewrite
   99  sudo systemctl restart apache2
  100  cd /var/www/html/
  101  cd backend/
  102  php artisan migrate
  103  sudo mysql
  104  sudo mysql -u root -p
  105  sudo mysql -u root
  106  sudo mysql -u root -p
  107  php artisan migrate
  108  tail -f /var/www/html/backend/storage/logs/laravel.log
  109  sudo mysql -u root -p password
  110  sudo mysql -u root -p bookstore_db
  111  php artisan tinker
  112  sudo systemctl restart apache2
  113  cd ~
  114  ls
  115  sudo rm backend.zip
  116  ls
  117  ls -l
  118  sudo mv backend.zip /var/www/html/
  119  cd /var/www/html/
  120  ls
  121  sudo mv backend backend-backup
  122  sudo unzip backend.zip
  123  ls
  124  ls -l
  125  sudo chown -R www-data:www-data backend
  126  sudo chmod -R 755 backend
  127  cd backend
  128  sudo chmod 777 storage/
  129  ls
  130  sudo vim .env
  131  ls -a
  132  cd ~
  133  ls
  134  ls -a
  135  sudo mv .env /var/www/html/backend
  136  cd /var/www/html/backend
  137  ls
  138  ls -a
  139  sudo vim .env
  140  sudo systemctl restart apache2
  141  php artisan tinker
  142  cd ..
  143  php artisan tinker
  144  cd backend
  145  composer install
  146  mkdir vendor
  147  sudo mkdir vendor
  148  ls -l
  149  composer install
  150  sudo composer install
  151  ls
  152  ls vendor -lk
  153  cat composer.json
  154  ls
  155  sudo vim config/app.php
  156  sudo rm vendor/
  157  sudo rm -rf vendor/
  158  sudo composer install
  159  php artisan config:cache
  160  php artisan cache:clear
  161  sudo composer require laravel/sanctum:^4.0
  162  php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
  163  sudo php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
  164  sudo php artisan migrate
  165  sudo systemctl restart apache2
  166  ls
  167  cd routes/
  168  ls
  169  sudo vim api.php
  170  cd ..
  171  cd..
  172  ls
  173  cd ..
  174  sudo chown -R www-data:www-data backend
  175  sudo systemctl restart apache2
  176  ls -l
  177  cd backend
  178  ls
  179  ls -l
  180  ls
  181  sudo vim .env
  182  cd storage/
  183  ls -l
  184  cd logs/
  185  ls -l
  186  sudo vim laravel.log
  187  sudo cp laravel.log ~/error.log
  188  cd ../../..
  189  sudo composer require aws/aws-sdk-php
  190  ls -l
  191  sudo chown -R www-data:www-data backend
  192  ls
  193  sudo rm backend.zip
  194  cd ~
  195  ls
  196  cd /var/www/h
  197  cd /var/www/html/
  198  ls
  199  sudo -u www-data backend ~/
  200  sudo -u www-data cp backend ~/
  201  sudo -u www-data cp -r backend ~/
  202  sudo cp -r backend ~/
  203  cd ~
  204  ls
  205  sudo rm -rf backend/
  206  cd /var/www/html/
  207  ls
  208  sudo zip backend
  209  sudo apt install zip -y
  210  sudo zip -r backend.zip backend
  211  ls
  212  sudo mv backend.zip ~
  213  ls
  214  cd composear
  215  cd composer
  216  ls -l
  217  cd backend
  218  ls
  219  sudo vim composer.lock
  220  composer require aws/aws-sdk-php
  221  sudo composer require aws/aws-sdk-php
  222  ls -l
  223  cd ..
  224  sudo chown -R www-data:www-data backend
  225  ls
  226  cd backend/app/Http/Controllers/API/
  227  sudo touch CustomerAccountController.php
  228  sudo vim CustomerAccountController.php
  229  sudo rm CustomerAccountController.php
  230  cd ~
  231  ls
  232  sudo mv CustomerAccountController.php /var/www/html/backend/
  233  cd /var/www/html/
  234  ls
  235  cd backend
  236  ls
  237  sudo mv CustomerAccountController.php app/Http/Controllers/API/
  238  cd ~
  239  sudo mv CustomerOrderController.php /var/www/html/backend/app/Http/Controllers/API/
  240  sudo mv CustomerAccount.php /var/www/html/backend/app/Models/
  241  ls
  242  cd /var/www/html/
  243  ls
  244  sudo mv backend-backup/ Backup
  245  sudo vim backend/app/Models/Order.php
  246  cd ~
  247  ls
  248  sudo mv 2024_07_09_000000_create_customer_accounts_table.php /var/www/html/backend/database/migrations/
  249  sudo mv 2024_07_09_100000_add_account_id_to_orders_table.php /var/www/html/backend/database/migrations/
  250  cd /var/www/html/backend/
  251  sudo vim routes/api.php
  252  sudo cp routes/api.php ~/
  253  cd routes/
  254  sudo rm api.php
  255  sudo mv ~/api.php .
  256  ls
  257  cd ~
  258  ls
  259  cd /var/www/html/
  260  ls -l
  261  sudo chown www-data:www-data backend/
  262  cd backend/
  263  ls -l
  264  cd routes/
  265  ls -l
  266  cd ../..
  267  sudo chown -R www-data:www-data backend/
  268  ls -l backend/routes/
  269  sudo chmod 755 backend/routes/api.php
  270  ls -l backend/routes/
  271  ls -l backend/app/Http/Controllers/API/
  272  sudo chmod 755 backend/app/Http/Controllers/API/
  273  ls -l backend/app/Http/Controllers/API/
  274  sudo chmod 755 -R backend/app/Http/Controllers/API/
  275  ls -l backend/app/Http/Controllers/API/
  276  ls -l backend/app/Models/
  277  sudo chmod 755 -R backend/app/Models/
  278  ls -l backend/app/Models/
  279  ls -l backend/database/migrations/
  280  sudo chmod 755 -R backend/database/migrations/
  281  cd backend/
  282  php artisan migrate
  283  sudo mv ~/2025_04_26_update_cart_table_user_id_column.php ./database/migrations/
  284  php artisan migration
  285  sudo php artisan migration
  286  sudo php artisan migrate
  287  ls -l database/migrations/
  288  sudo chmod 755 database/migrations/2025_04_26_update_cart_table_user_id_column.php
  289  sudo php artisan migrate
  290  php artisan migrate:status
  291  sudo php artisan migrate --path=database/migrations/2025_04_26_update_cart_table_user_id_column.php
  292  composer require doctrine/dbal
  293  sudo composer require doctrine/dbal
  294  sudo php artisan migrate --path=database/migrations/2025_04_26_update_cart_table_user_id_column.php
  295  composer update doctrine/dbal
  296  php artisan migrate
  297  sudo php artisan migrate
  298  cd database/migrations/
  299  ls
  300  sudo rm 2025_04_26_042741_create_personal_access_tokens_table.php
  301  sudo vim 2025_04_21_142030_create_personal_access_tokens_table.php
  302  cd ../..
  303  sudo php artisan migrate
  304  cd database/migrations/
  305  ls
  306  sudo rm 2025_04_26_update_cart_table_user_id_column.php
  307  sudo mv ~/2025_04_26_update_cart_table_user_id_column.php .
  308  sudo chmod 755 2025_04_26_update_cart_table_user_id_column.php
  309  cd ../..
  310  sudo php artisan migrate
  311  cd database/migrations/
  312  ls
  313  sudo rm 2025_04_26_update_cart_table_user_id_column.php
  314  sudo mv ~/2025_04_26_update_cart_table_user_id_column.php .
  315  ls
  316  sudo chmod 755 2025_04_26_update_cart_table_user_id_column.php
  317  cd ../..
  318  php artisan migrate
  319  cd ..
  320  ls
  321  sudo zip -r backend.zip backend/
  322  ls
  323  sudo mv backend.zip ~
  324  ls ~
  325  ls
  326  ls -l ~/
  327  cd backend/
  328  ls
  329  cd app/Models/
  330  ls
  331  sudo rm Cart.php
  332  sudo mv ~/Cart.php .
  333  sudo chmod 755 Cart.php
  334  ls
  335  cd ..
  336  ls
  337  cd Http/Controllers/
  338  ls
  339  cd API/
  340  ls
  341  sudo rm CartController.php
  342  sudo mv ~/CartController.php .
  343  sudo chmod 755 CartController.php
  344  ls
  345  sudo systemctl restart apache2
  346  cd /var/www/html/
  347  systemctl daemon-reload
  348  cd /var/www/html/
  349  cd backend/
  350  sudo vim app/Http/Controllers/API/OrderController.php
  351  ls -l app/Http/Controllers/API/
  352  sudo chown www-data:www-data app/Http/Controllers/API/CartController.php
  353  sudo vim app/Models/Order.php
  354  sudo systemctl restart apache2
  355  ls -l app/Models/
  356  sudo chown www-data:www-data app/Models/Cart.php
  357  sudo vim app/Http/Controllers/API/OrderController.php
  358  sudo vim app/Models/Order.php
  359  systemctl daemon-reload
  360  sudo vim app/Models/Order.php
  361  sudo vim app/Http/Controllers/API/BookController.php
  362  sudo vim routes/api.php
  363  cd app/Http/Controllers/
  364  ls
  365  cd API/
  366  ls
  367  sudo rm BookController.php
  368  sudo mv ~/BookController.php .
  369  sudo chown www-data:www-data BookController.php
  370  sudo chmod 755 BookController.php
  371  ls
  372  cd /var/www/html/backend/
  373  cd app/Http/Controllers/API/
  374  sudo rm BookController.php
  375  sudo mv ~/BookController.php .
  376  sudo chown www-data:www-data BookController.php
  377  sudo chmod 755 BookController.php
  378  cd /var/www/html/
  379  cd backend/
  380  sudo vim routes/api.php
  381  sudo vim app/Http/Controllers/API/
  382  cd app/Http/Controllers/API/
  383  sudo rm OrderController.php
  384  sudo mv ~/OrderController.php .
  385  sudo chown www-data:www-data OrderController.php
  386  sudo chmod 755 OrderController.php
  387  cd /var/www/html/backend/
  388  sudo vim routes/api.php
  389  cd /var/www/html/backend/
  390  sudo vim app/Http/Controllers/API/BookController.php
  391  cd app/Http/Controllers/API/
  392  sudo rm BookController.php
  393  sudo mv ~/BookController.php .
  394  sudo chown www-data:www-data BookController.php
  395  sudo chmod 755 BookController.php
  396  cd /var/www/html/backend/
  397  sudo vim routes/api.php
  398  cd /var/www/html/backend/
  399  ls
  400  cd /var/www/html/backend/
  401  ls routes/api.php
  402  cd routes/ap
  403  cd routes/
  404  ls
  405  sudo vim api.php
  406  git --version
  407  cd /var/www/html/
  408  cd backend/
  409  ls
  410  git init
  411  sudo git init
  412  git add .
  413  sudo git add .
  414  ls
  415  ls -l
  416  ls -a
  417  ls .git
  418  ls -l .git
  419  sudo git add .
  420  sudo git config --global --add safe.directory /var/www/html/backend/
  421  git add .
  422  sudo git add .
  423  sudo git config --global --add safe.directory /var/www/html/backend/
  424  git add .
  425  sudo git add .
  426  clear
  427  sudo git add .
  428  sudo git config --global --add safe.directory /var/www/html/backend/
  429  sudo git add .
  430  git config --global --list
  431  sudo git config --global --list
  432  sudo git config --global user.name "socheatsorng"
  433  sudo git config --global user.email "<EMAIL>"
  434  sudo git config --global --add safe.directory /var/www/html/backend/
  435  sudo git config --global --list
  436  sudo nano /root/.gitconfig
  437  sudo git config --global --list\
  438  sudo git status
  439  git status
  440  cd ..
  441  ls -l
  442  cd backend/
  443  ls -l
  444  ls -l -a
  445  sudo -u www-data
  446  sudo chown www-data .git
  447  ls -a -l
  448  sudo chown www-data:www-data .git
  449  cd ..
  450  ls
  451  ls -l
  452  ls -l ~/
  453  sudo rm ~/backend.zip
  454  sudo zip backend/
  455  sudo zip backend.zip backend/
  456  ls
  457  sudo rm backend.zip
  458  sudo zip -r backend.zip backend/
  459  ls -l
  460  sudo mv backend.zip ~/
  461  cd backend/
  462  git status
  463  ls -a -l
  464  cd .git/
  465  ls -l
  466  cd ..
  467  sudo chown -R www-data:www-data .git
  468  git status
  469  sudo git status
  470  wsl
  471  cd /var/www/html/LaravelReact/backend
  472  ls -l
  473  sudo vim routes/api.php
  474  ls -l -a
  475  cd ../..
  476  ls -l
  477  sudo chmod -R 755 LaravelReact/
  478  cd LaravelReact/
  479  cd backend
  480  ls -l storage/
  481  ls -l
  482  git clone
  483  cd /var/www/html/backend/
  484  ls -l
  485  ls -a
  486  git status
  487  git --global --list
  488  clear
  489  git status
  490  git config --global --add safe.directory /var/www/html/backend/
  491  git status
  492  git config --add safe.directory /var/www/html/backend/
  493  sudo chown -R ubuntu:ubuntu /var/www/html/backend/
  494  git status
  495  git init
  496  git status
  497  git add .
  498  git commit -m "Add modified files"
  499  ssh
  500  cd ..
  501  ls
  502  https://github.com/SocheatSorng/LaravelReact.git
  503  git clone
  504  git clone https://github.com/SocheatSorng/LaravelReact.git
  505  sudo git clone https://github.com/SocheatSorng/LaravelReact.git
  506  ls -l
  507  sudo chown ubuntu:ubuntu LaravelReact/
  508  sudo chown -R ubuntu:ubuntu LaravelReact/
  509  cd LaravelReact/
  510  ls -l
  511  cd /etc/apache2/
  512  ls -l
  513  cd apache2.conf
  514  cd sites-available/
  515  ls
  516  sudo nano 000-default.conf
  517  sudo vim 000-default.conf
  518  sudo vim default-ssl.conf
  519  cd ..
  520  sudo vim conf-enabled/
  521  cd conf-enabled/
  522  ls
  523  cd ..
  524  ls
  525  ls -l
  526  sudo vim apache2.conf
  527  history
  528  ls
  529  sudo vim sites-available/
  530  cd sites-available/
  531  sudo vim 000-default.conf
  532  cd /var/www/html/backend/
  533  ls -l
  534  cd config/
  535  ls -l
  536  ls -a -l
  537  cd ..
  538  cd routes/
  539  ls -l -a
  540  sudo vim api.php
  541  sudo vim /etc/apache2/sites-available/000-default.conf
  542  sudo systemctl restart apache2
  543  sudo vim /etc/apache2/sites-available/000-default.conf
  544  sudo systemctl restart apache2
  545  sudo vim /etc/apache2/sites-available/000-default.conf
  546  sudo cp /var/www/html/backend/.env /var/www/html/LaravelReact/backend
  547  sudo vim ../.env
  548  sudo vim /etc/apache2/sites-available/000-default.conf
  549  sudo systemctl restart apache2
  550  sudo vim /etc/apache2/sites-available/000-default.conf
  551  sudo vim /etc/apache2/apache2.conf
  552  sudo vim /etc/apache2/sites-available/cd ..
  553  sudo systemctl restart apache2
  554  cd ..
  555  ls -l
  556  ls -l storage/
  557  sudo chown -R www-data:www-data /var/www/html/backend/
  558  sudo systemctl restart apache2
  559  sudo chmod -R 755 /var/www/html/LaravelReact/backend/storage/
  560  ls -l ../LaravelReact/backend
  561  sudo chown -R www-data:www-data /var/www/html/LaravelReact/
  562  ls -l ../LaravelReact/
  563  cd ~/
  564  sudo apt install rsync
  565  sudo rsync -av --ignore-existing /var/www/html/backend/ /var/www/html/LaravelReact/backend/
  566  cd /var/www/html/LaravelReact/backend
  567  git pull
  568  cd ../..
  569  sudo chown -R ubuntu:ubuntu LaravelReact/
  570  cd LaravelReact/
  571  git pull
  572  git status
  573  cd ..
  574  ls -l
  575  sudo mv backend/ backend-old
  576  sudo systemctl restart apache2
  577  cd LaravelReact/backend
  578  git pull
  579  cd ..
  580  git pull
  581  git status
  582  git pull
  583  git restore backend/app/Http/Controllers/API/CustomerOrderController.php
  584  git pull
  585  git restore backend/app/Http/Controllers/API/OrderController.php backend/bootstrap/app.php backend/routes/api.php backend/routes/api.php
  586  git pull
  587  sudo vim backend/.env
  588  sudo echo ~/env.txt
  589  sudo cat ~/env.txt
  590  sudo cat ~/env.txt backend/.env
  591  sudo cat ~/env.txt > backend/.env
  592  sudo cat backend/.env
  593  sudo vim backend/.env
  594  sudo cp /var/www/html/backend-old/.env /var/www/html/LaravelReact/backend/.env
  595  sudo cat backend/.env
  596  sudo echo ~/env.txt >> backend/.env
  597  sudo cat backend/.env
  598  sudo cat ~/env.txt >> backend/.env
  599  sudo vim backend/.env
  600  sudo systemctl restart apache2
  601  sudo cat backend/storage/logs/laravel.log
  602  git reflog
  603  git log --oneline
  604  git cherry-pick fd6aee4
  605  git restore backend/app/Http/Controllers/API/CustomerOrderController.php
  606  git cherry-pick --abort
  607  git restore backend/app/Http/Controllers/API/CustomerOrderController.php
  608  git restore backend/app/Http/Controllers/API/OrderController.php
  609  git restore backend/routes/api.php
  610  git cherry-pick fd6aee4
  611  git cherry-pick --abort
  612  git restore backend/app/Http/Controllers/API/CustomerOrderController.php
  613  git restore backend/app/Http/Controllers/API/OrderController.php
  614  git restore backend/routes/api.php
  615  git status
  616  git restore backend/app/Http/Controllers/API/CustomerOrderController.php
  617  git restore backend/app/Http/Controllers/API/OrderController.php
  618  git restore backend/routes/api.php
  619  git status
  620  git cherry-pick fd6aee4
  621  git cherry-pick --abort
  622  git cherry-pick fd6aee4
  623  git status
  624  sudo vim backend/app/Http/Controllers/API/CustomerOrderController.php
  625  git status
  626  sudo vim backend/app/Http/Controllers/API/CustomerOrderController.php
  627  git cherry-pick --abort
  628  sudo mv backend/app/Http/Controllers/API/CustomerOrderController.php ../
  629  ls ../
  630  sudo mv backend/app/Http/Controllers/API/OrderController.php ../
  631  sudo mv backend/routes/api.php ../
  632  git cherry-pick fd6aee4
  633  git status
  634  git rm backend/app/Http/Controllers/API/CustomerOrderController.php
  635  git rm backend/app/Http/Controllers/API/OrderController.php
  636  git rm backend/routes/api.php
  637  git rm -f backend/routes/api.php
  638  git cherry-pick --continue
  639  ls -l backend/app/Http/Controllers/API/
  640  sudo chmod -R 755 backend/app/Http/Controllers/API/
  641  ls -l backend/app/Http/Controllers/API/
  642  git reflog
  643  sudo mv ~/CustomerOrderController.php backend/app/Http/Controllers/API/
  644  sudo chmod 755 backend/app/Http/Controllers/API/CustomerOrderController.php
  645  sudo mv ~/OrderController.php backend/app/Http/Controllers/API/
  646  sudo chmod 755 backend/app/Http/Controllers/API/OrderController.php
  647  sudo mv ~/api.php backend/routes/
  648  sudo chmod 755 backend/routes/api.php
  649  ls -l backend/routes/api.php
  650  ls -l backend/routes/
  651  ls -l backend/app/Http/Controllers/API/
  652  cd backend
  653  ls -l
  654  cd ..
  655  sudo chown -R ubuntu:ubuntu backend
  656  sudo chmod -R 755 backend/
  657  sudo chown -R ubuntu:ubuntu backend/
  658  sudo vim backend/.env
  659  cd backend/app/Http/Controllers/API/
  660  ls -l
  661  sudo vim TelegramController.php
  662  sudo vim OrderController.php
  663  sudo systemctl restart apache2
  664  cd ../../../../
  665  cd ..
  666  git pull
  667  git status
  668  git cherry-pick --abort
  669  git log --oneline
  670  git fetch
  671  git pull
  672  cd backend
  673  git pull
  674  cd ..
  675  git reflog
  676  git restore backend/app/Http/Controllers/API/CustomerOrderController.php
  677  git status
  678  git clean 0f
  679  clean clean -f
  680  git clean -f
  681  git pull
  682  git pull origin
  683  git pull origin HEAD
  684  git pull origin main
  685  cd ..
  686  sudo mv LaravelReact/ LaravelReact-old/
  687  git clone https://github.com/socheatsorng/LaravelReact.git
  688  sudo git clone https://github.com/socheatsorng/LaravelReact.git
  689  ls -l
  690  sudo rsync backend-old/ LaravelReact/
  691  sudo rsync -R backend-old/ LaravelReact/
  692  sudo rsync -av -ignore-existing /var/www/html/backend-old/ /var/www/html/LaravelReact/
  693  sudo cp backend-old/.env LaravelReact/backend/
  694  sudo vim LaravelReact/backend.env
  695  sudo vim LaravelReact/backend/.env
  696  sudo cat ~/.env
  697  sudo cat ~/env.txt
  698  sudo cat ~/env.txt >> LaravelReact/backend/.env
  699  ls -l
  700  sudo chown -R ubuntu:ubuntu LaravelReact/
  701  ls -l
  702  cd LaravelReact
  703  ls -l
  704  cd ..
  705  sudo chmod 755 -R LaravelReact
  706  sudo echo ~/env.txt
  707  sudo cat ~/env.txt >> LaravelReact/backend/.env
  708  sudo cat LaravelReact/backend/.env
  709  sudo systemctl restart apache2
  710  cd LaravelReact/
  711  ls -l
  712  cd backend
  713  ls -l
  714  cd ..
  715  ls -l
  716  sudo rsync -av --ignore-existing /var/www/html/backend-old/ /var/www/html/LaravelReact/backend
  717  sudo chown -R ubuntu:ubuntu LaravelReact/backend
  718  sudo chmod -R 755 LaravelReact/backend
  719  sudo systemctl restart apache2
  720  sudo rm -rf LaravelReact-old/
  721  ls -l
  722  sudo rm CustomerOrderController.php OrderController.php api.php
  723  ls -l Backup/
  724  cd LaravelReact/
  725  git pull
  726  git restore backend/app/Http/Middleware/PayPalReturnCheck.php
  727  git restore backend/routes/api.php
  728  git pull
  729  sudo mv ~/Cors.php backend/app/Http/Middleware/
  730  sudo vim backend/bootstrap/app.php
  731  sudo vim backend/routes/api.php
  732  sudo vim backend/.env
  733  sudo systemctl restart apache2
  734  sudo vim backend/.env
  735  sudo mv ~/Cors.php backend/app/Http/Middleware/
  736  sudo vim backend/app/Http/Middleware/Cors.php
  737  sudo vim backend/bootstrap/app.php
  738  sudo systemctl restart apache2
  739  cd backend
  740  sudo vim storage/logs/laravel.log
  741  sudo echo " " > storage/logs/laravel.log
  742  sudo vim storage/logs/laravel.log
  743  sudo systemctl restart apache2
  744  sudo mv ~/CorsMiddleware.php app/Http/Middleware/
  745  sudo vim bootstrap/app.php
  746  sudo systemctl restart apache3
  747  sudo systemctl restart apache2
  748  sudo vim .env
  749  sudo systemctl restart apache2
  750  sudo mv ~/paypal-return.html public/
  751  cd public/
  752  sudo rm paypal-return.html
  753  sudo mv ~/paypal-return.html .
  754  cd ..
  755  sudo vim storage/logs/laravel.log
  756  cd app/Http/Middleware/
  757  ls -l
  758  sudo rm Cors.php
  759  sudo rm CorsMiddleware.php
  760  sudo mv ~/Cors.php .
  761  ls -l
  762  sudo chmod -R 755 .
  763  ls -l
  764  cd ../../
  765  cd ..
  766  cd bootstrap/
  767  sudo vim app.php
  768  cd ..
  769  cd app/Services/
  770  ls -l ../../bootstrap/
  771  sudo vim TelegramBotService.php
  772  cd ../..
  773  sudo vim routes/api.php
  774  cd app/Http/Middleware/
  775  sudo vim PayPalReturnCheck.php
  776  sudo systemctl restart apache2
  777  cd ..
  778  cd ../..
  779  sudo vim storage/logs/laravel.log
  780  sudo mv ~/.htaccess public/
  781  sudo ls -l app/Services/
  782  sudo chmod 755 app/Services/
  783  ls -l app/Services/e
  784  ls -l app/Services
  785  sudo vim config/sanctum.php
  786  sudo vim .env
  787  sudo systemctl restart apache2
  788  cd /var/www/html/LaravelReact/backend
  789  sudo mv ~/cors-handler.php public/
  790  sudo vim public/index.php
  791  sudo mv ~/api-proxy.php public/
  792  sudo mv ~/test-api.html public/
  793  sudo systemctl restart apache2
  794  sudo vim storage/logs/laravel.log
  795  ls -l storage/logs/
  796  sudo chown -R www-data:www-data storage/
  797  sudo chown -R www-data:www-data bootstrap/cache/
  798  sudo chmod -R 755 storage/
  799  sudo chmod -R 755 bootstrap/cache/
  800  sudo systemctl restart apache
  801  sudo systemctl restart apache2
  802  sudo vim routes/api.php
  803  ls -l app/Http/Middleware/
  804  sudo rm app/Http/Middleware/Cors.php
  805  sudo mv ~/CorsMiddleware.php app/Http/Middleware/
  806  sudo chown ubuntu:ubuntu app/Http/Middleware/CorsMiddleware.php
  807  sudo chmod 755 app/Http/Middleware/CorsMiddleware.php
  808  sudo vim bootstrap/app.php
  809  sudo systemctl restart apache2
  810  sudo mv ~/test-login.html public/
  811  sudo rm public/test-login.html
  812  sudo mv ~/test-login.html public/
  813  sudo chmod 755 public/test-login.html
  814  sudo rm routes/api.php
  815  sudo mv ~/api.php routes/
  816  sudo chmod 755 routes/api.php
  817  sudo mv ~/api-key-generator.php public/
  818  sudo systemctl restart apache2
  819  sudo mv ~/test-api-key.php public/
  820  sudo chmod 755 public/test-api-key.php
  821  sudo systemctl restart apache2
  822  sudo mv ~/test-customer-accounts.php public/
  823  sudo chmod 755 public/test-customer-accounts.php
  824  sudo mv ~/create-test-account.php public/
  825  sudo chmod 755 public/create-test-account.php
  826  sudo vim .env
  827  sudo systemctl restart apache2
  828  sudo mv ~/db-connection-test.php public/
  829  sudo chmod 755 public/db-connection-test.php
  830  sudo rm public/db-connection-test.php
  831  sudo mv ~/db-connection-test.php public/
  832  sudo chmod 755 public/db-connection-test.php
  833  sudo mv ~/check-mysql-service.php public/
  834  sudo mv ~/update-db-config.php public/
  835  sudo mv ~/check-laravel-env.php public/
  836  sudo mv ~/db-config-form.html public/
  837  cd ..
  838  rsync -av -ignore-existing ../backend-old/ backend/
  839  rsync -av --ignore-existing ../backend-old/ backend/
  840  cd ..
  841  sudo rm -rf LaravelReact/
  842  git clone https://github.com/socheatsorng/LaravelReact.git
  843  sudo git clone https://github.com/socheatsorng/LaravelReact.git
  844  sudo chown -R www-data:www-data LaravelReact/
  845  sudo chmod -R 755 LaravelReact/
  846  sudo systemctl restart apache2
  847  sudo rsync -av --ignore-existing backend-old/ LaravelReact/backend/
  848  sudo chown -R www-data:www-data LaravelReact/
  849  sudo chmod -R 755 LaravelReact/
  850  ls -l LaravelReact/backend
  851  sudo cat backend-old/.env >> ~/env.txt
  852  sudo vim ~/env.txt
  853  sudo cp ~/env.txt LaravelReact/backend
  854  sudo systemctl restart apache2
  855  history