#!/bin/bash

# Laravel EC2 Setup Script - Optimized for GitHub Project Deployment
# This script automates the setup of <PERSON><PERSON> on EC2 with GitHub integration

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
GITHUB_REPO="https://github.com/SocheatSorng/LaravelReact.git"
PROJECT_NAME="LaravelReact"
WEB_ROOT="/var/www/html"
PROJECT_PATH="$WEB_ROOT/$PROJECT_NAME"
LARAVEL_PATH="$PROJECT_PATH/backend"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Update system packages
print_status "Updating system packages..."
sudo apt-get update -y
sudo apt-get upgrade -y
print_success "System packages updated"

# Install required packages
print_status "Installing required packages..."
sudo apt-get install -y \
    apache2 \
    php \
    php-cli \
    php-fpm \
    php-mysql \
    php-mbstring \
    php-zip \
    php-gd \
    php-json \
    php-curl \
    php-xml \
    php-bcmath \
    php-tokenizer \
    mysql-server \
    git \
    unzip \
    curl \
    vim \
    rsync

print_success "Required packages installed"

# Install Composer
print_status "Installing Composer..."
if ! command_exists composer; then
    curl -sS https://getcomposer.org/installer | php
    sudo mv composer.phar /usr/local/bin/composer
    sudo chmod +x /usr/local/bin/composer
    print_success "Composer installed"
else
    print_warning "Composer already installed"
fi

# Configure Apache
print_status "Configuring Apache..."
sudo a2enmod rewrite
sudo a2enmod ssl

# Backup original Apache configuration
sudo mkdir -p ~/Backup
sudo cp /etc/apache2/sites-available/000-default.conf ~/Backup/000-default.conf.backup

# Create optimized Apache virtual host configuration
sudo tee /etc/apache2/sites-available/000-default.conf > /dev/null <<EOF
<VirtualHost *:80>
    ServerAdmin webmaster@localhost
    DocumentRoot $LARAVEL_PATH/public

    <Directory $LARAVEL_PATH/public>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
    </Directory>

    ErrorLog \${APACHE_LOG_DIR}/error.log
    CustomLog \${APACHE_LOG_DIR}/access.log combined
</VirtualHost>
EOF

print_success "Apache configured"

# Clone or update GitHub repository
print_status "Setting up Laravel project from GitHub..."
cd $WEB_ROOT

if [ -d "$PROJECT_PATH" ]; then
    print_warning "Project directory exists. Backing up and updating..."
    sudo mv "$PROJECT_PATH" "${PROJECT_PATH}-backup-$(date +%Y%m%d-%H%M%S)"
fi

sudo git clone $GITHUB_REPO $PROJECT_NAME
sudo chown -R www-data:www-data $PROJECT_PATH
sudo chmod -R 755 $PROJECT_PATH

print_success "Project cloned from GitHub"

# Setup Laravel environment
print_status "Setting up Laravel environment..."
cd $LARAVEL_PATH

# Install PHP dependencies
print_status "Installing PHP dependencies..."
sudo -u www-data composer install --no-dev --optimize-autoloader

# Set proper permissions
print_status "Setting proper permissions..."
sudo chown -R www-data:www-data $LARAVEL_PATH
sudo chmod -R 755 $LARAVEL_PATH
sudo chmod -R 775 $LARAVEL_PATH/storage
sudo chmod -R 775 $LARAVEL_PATH/bootstrap/cache

# Create .env file if it doesn't exist
if [ ! -f "$LARAVEL_PATH/.env" ]; then
    print_status "Creating .env file..."
    sudo -u www-data cp $LARAVEL_PATH/.env.example $LARAVEL_PATH/.env

    # Generate application key
    sudo -u www-data php artisan key:generate
    print_success ".env file created and application key generated"
else
    print_warning ".env file already exists"
fi

# Configure MySQL
print_status "Configuring MySQL..."
sudo mysql_secure_installation

# Create database and user
print_status "Setting up database..."
read -p "Enter database name (default: laravel_db): " DB_NAME
DB_NAME=${DB_NAME:-laravel_db}

read -p "Enter database username (default: laravel_user): " DB_USER
DB_USER=${DB_USER:-laravel_user}

read -s -p "Enter database password: " DB_PASS
echo

# Create database and user
sudo mysql -e "CREATE DATABASE IF NOT EXISTS $DB_NAME;"
sudo mysql -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASS';"
sudo mysql -e "GRANT ALL PRIVILEGES ON $DB_NAME.* TO '$DB_USER'@'localhost';"
sudo mysql -e "FLUSH PRIVILEGES;"

print_success "Database and user created"

# Update .env file with database configuration
print_status "Updating .env file with database configuration..."
sudo sed -i "s/DB_DATABASE=.*/DB_DATABASE=$DB_NAME/" $LARAVEL_PATH/.env
sudo sed -i "s/DB_USERNAME=.*/DB_USERNAME=$DB_USER/" $LARAVEL_PATH/.env
sudo sed -i "s/DB_PASSWORD=.*/DB_PASSWORD=$DB_PASS/" $LARAVEL_PATH/.env

print_success ".env file updated with database configuration"

# Install Laravel Sanctum (if needed)
print_status "Installing Laravel Sanctum..."
if grep -q "laravel/sanctum" composer.json; then
    sudo -u www-data composer require laravel/sanctum
    sudo -u www-data php artisan vendor:publish --provider="Laravel\Sanctum\SanctumServiceProvider"
    print_success "Laravel Sanctum installed"
else
    print_warning "Laravel Sanctum not found in composer.json, skipping..."
fi

# Run database migrations
print_status "Running database migrations..."
sudo -u www-data php artisan migrate --force

# Clear and cache configuration
print_status "Optimizing Laravel..."
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache

print_success "Laravel optimized"

# Restart Apache
print_status "Restarting Apache..."
sudo systemctl restart apache2
sudo systemctl enable apache2

print_success "Apache restarted and enabled"

# Setup firewall (optional)
print_status "Configuring firewall..."
if command_exists ufw; then
    sudo ufw allow 22/tcp
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    sudo ufw --force enable
    print_success "Firewall configured"
else
    print_warning "UFW not installed, skipping firewall configuration"
fi

# Final status check
print_status "Performing final status check..."
if sudo systemctl is-active --quiet apache2; then
    print_success "Apache is running"
else
    print_error "Apache is not running"
fi

if sudo systemctl is-active --quiet mysql; then
    print_success "MySQL is running"
else
    print_error "MySQL is not running"
fi

# Display final information
echo
echo "=========================================="
echo -e "${GREEN}Laravel Setup Complete!${NC}"
echo "=========================================="
echo "Project Path: $LARAVEL_PATH"
echo "Web Root: $LARAVEL_PATH/public"
echo "Database: $DB_NAME"
echo "Database User: $DB_USER"
echo
echo "Next steps:"
echo "1. Configure your .env file with additional settings"
echo "2. Set up SSL certificate (recommended)"
echo "3. Configure your domain/DNS settings"
echo "4. Test your application"
echo
echo "To update your project from GitHub:"
echo "cd $PROJECT_PATH && git pull origin main"
echo
echo "To view Laravel logs:"
echo "tail -f $LARAVEL_PATH/storage/logs/laravel.log"
echo "=========================================="