#!/bin/bash

# Apache Installation and Configuration Script for Laravel
# This script installs and configures Apache web server for Laravel applications

echo "=========================================="
echo "Apache Installation Script for Laravel"
echo "=========================================="

# Check if running with sudo
if [ "$EUID" -ne 0 ]; then
    echo "[ERROR] Please run this script with sudo"
    exit 1
fi

# Update package list
echo "[INFO] Updating package list..."
apt-get update -y

# Install Apache
echo "[INFO] Installing Apache web server..."
apt-get install -y apache2

# Start and enable Apache
echo "[INFO] Starting and enabling Apache service..."
systemctl start apache2
systemctl enable apache2

# Check Apache status
if systemctl is-active --quiet apache2; then
    echo "[SUCCESS] Apache is running"
else
    echo "[ERROR] Apache failed to start"
    systemctl status apache2
    exit 1
fi

# Install PHP and required modules
echo "[INFO] Installing PHP and Apache PHP module..."
apt-get install -y php libapache2-mod-php php-mysql php-cli php-fpm php-mbstring php-zip php-gd php-json php-curl php-xml php-bcmath php-tokenizer

# Enable required Apache modules
echo "[INFO] Enabling Apache modules..."
a2enmod rewrite
a2enmod ssl
a2enmod headers
a2enmod expires
a2enmod php7.4 2>/dev/null || a2enmod php8.0 2>/dev/null || a2enmod php8.1 2>/dev/null || a2enmod php8.2 2>/dev/null || echo "[WARNING] Could not determine PHP version module"

# Test Apache configuration
echo "[INFO] Testing Apache configuration..."
if apache2ctl configtest; then
    echo "[SUCCESS] Apache configuration is valid"
else
    echo "[ERROR] Apache configuration has errors"
    exit 1
fi

# Create backup directory
echo "[INFO] Creating backup directory..."
mkdir -p /home/<USER>/Backup

# Backup default Apache configuration
echo "[INFO] Backing up default Apache configuration..."
cp /etc/apache2/sites-available/000-default.conf /home/<USER>/Backup/000-default.conf.original

# Create Laravel-optimized Apache configuration
echo "[INFO] Creating Laravel-optimized Apache configuration..."
tee /etc/apache2/sites-available/000-default.conf > /dev/null << 'EOL'
<VirtualHost *:80>
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/html/LaravelReact/backend/public

    <Directory /var/www/html/LaravelReact/backend/public>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        # Laravel-specific directives
        RewriteEngine On
        
        # Handle Angular and Vue.js routes
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>

    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"
    Header always set Content-Security-Policy "default-src 'self'"

    # Hide Apache version
    ServerTokens Prod
    ServerSignature Off

    # Logging
    ErrorLog ${APACHE_LOG_DIR}/laravel_error.log
    CustomLog ${APACHE_LOG_DIR}/laravel_access.log combined
    
    # Log level
    LogLevel warn
</VirtualHost>
EOL

# Create SSL configuration template
echo "[INFO] Creating SSL configuration template..."
tee /etc/apache2/sites-available/000-default-ssl.conf > /dev/null << 'EOL'
<IfModule mod_ssl.c>
<VirtualHost *:443>
    ServerAdmin webmaster@localhost
    DocumentRoot /var/www/html/LaravelReact/backend/public

    <Directory /var/www/html/LaravelReact/backend/public>
        Options Indexes FollowSymLinks
        AllowOverride All
        Require all granted
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php [QSA,L]
    </Directory>

    # SSL Configuration
    SSLEngine on
    # SSLCertificateFile /path/to/your/certificate.crt
    # SSLCertificateKeyFile /path/to/your/private.key
    # SSLCertificateChainFile /path/to/your/chain.crt

    # Security headers
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
    Header always set Strict-Transport-Security "max-age=63072000; includeSubDomains; preload"

    ErrorLog ${APACHE_LOG_DIR}/laravel_ssl_error.log
    CustomLog ${APACHE_LOG_DIR}/laravel_ssl_access.log combined
</VirtualHost>
</IfModule>
EOL

# Set proper ownership for backup
chown ubuntu:ubuntu /home/<USER>/Backup/000-default.conf.original

# Test new configuration
echo "[INFO] Testing new Apache configuration..."
if apache2ctl configtest; then
    echo "[SUCCESS] New Apache configuration is valid"
else
    echo "[ERROR] New Apache configuration has errors"
    echo "[INFO] Restoring original configuration..."
    cp /home/<USER>/Backup/000-default.conf.original /etc/apache2/sites-available/000-default.conf
    exit 1
fi

# Restart Apache
echo "[INFO] Restarting Apache with new configuration..."
systemctl restart apache2

# Final status check
if systemctl is-active --quiet apache2; then
    echo "[SUCCESS] Apache restarted successfully"
else
    echo "[ERROR] Apache failed to restart"
    systemctl status apache2
    exit 1
fi

# Display information
echo
echo "=========================================="
echo "Apache Installation Complete!"
echo "=========================================="
echo "Apache Status: $(systemctl is-active apache2)"
echo "Apache Version: $(apache2 -v | head -n1)"
echo "PHP Version: $(php -v | head -n1)"
echo
echo "Configuration files:"
echo "- Main config: /etc/apache2/apache2.conf"
echo "- Site config: /etc/apache2/sites-available/000-default.conf"
echo "- SSL template: /etc/apache2/sites-available/000-default-ssl.conf"
echo "- Backup: /home/<USER>/Backup/000-default.conf.original"
echo
echo "Log files:"
echo "- Error log: /var/log/apache2/laravel_error.log"
echo "- Access log: /var/log/apache2/laravel_access.log"
echo
echo "Enabled modules:"
a2enmod --list | grep -E "(rewrite|ssl|headers|expires|php)"
echo
echo "Next steps:"
echo "1. Install Laravel project"
echo "2. Set up SSL certificate (optional)"
echo "3. Configure firewall"
echo "=========================================="
