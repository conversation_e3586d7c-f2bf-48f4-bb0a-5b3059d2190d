#!/bin/bash

# Laravel Project Update Script
# This script updates your Laravel project from GitHub

set -e  # Exit on any error

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration variables
PROJECT_NAME="LaravelReact"
WEB_ROOT="/var/www/html"
PROJECT_PATH="$WEB_ROOT/$PROJECT_NAME"
LARAVEL_PATH="$PROJECT_PATH/backend"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if project exists
if [ ! -d "$PROJECT_PATH" ]; then
    print_error "Project directory $PROJECT_PATH does not exist!"
    print_status "Please run the main setup script first: ./configure-laravel-ec2.sh"
    exit 1
fi

print_status "Updating Laravel project from GitHub..."

# Navigate to project directory
cd $PROJECT_PATH

# Backup current .env file
print_status "Backing up current .env file..."
sudo cp $LARAVEL_PATH/.env $LARAVEL_PATH/.env.backup

# Stash any local changes
print_status "Stashing local changes..."
git stash

# Pull latest changes from GitHub
print_status "Pulling latest changes from GitHub..."
git pull origin main

# Navigate to Laravel directory
cd $LARAVEL_PATH

# Install/update dependencies
print_status "Updating PHP dependencies..."
sudo -u www-data composer install --no-dev --optimize-autoloader

# Restore .env file if it was overwritten
if [ -f "$LARAVEL_PATH/.env.backup" ]; then
    print_status "Restoring .env file..."
    sudo cp $LARAVEL_PATH/.env.backup $LARAVEL_PATH/.env
    sudo rm $LARAVEL_PATH/.env.backup
fi

# Run database migrations
print_status "Running database migrations..."
sudo -u www-data php artisan migrate --force

# Clear and cache configuration
print_status "Clearing and caching configuration..."
sudo -u www-data php artisan config:clear
sudo -u www-data php artisan cache:clear
sudo -u www-data php artisan route:clear
sudo -u www-data php artisan view:clear

# Optimize for production
print_status "Optimizing for production..."
sudo -u www-data php artisan config:cache
sudo -u www-data php artisan route:cache
sudo -u www-data php artisan view:cache

# Set proper permissions
print_status "Setting proper permissions..."
sudo chown -R www-data:www-data $PROJECT_PATH
sudo chmod -R 755 $PROJECT_PATH
sudo chmod -R 775 $LARAVEL_PATH/storage
sudo chmod -R 775 $LARAVEL_PATH/bootstrap/cache

# Restart Apache
print_status "Restarting Apache..."
sudo systemctl restart apache2

print_success "Project updated successfully!"
echo
echo "=========================================="
echo -e "${GREEN}Update Complete!${NC}"
echo "=========================================="
echo "Project Path: $LARAVEL_PATH"
echo "Web Root: $LARAVEL_PATH/public"
echo
echo "To view Laravel logs:"
echo "tail -f $LARAVEL_PATH/storage/logs/laravel.log"
echo "=========================================="
