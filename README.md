# Laravel EC2 Setup Scripts

This repository contains optimized scripts for setting up and managing Laravel applications on Amazon EC2 instances.

## Scripts Overview

### 1. `setup-laravel.sh` - **COMPLETE ALL-IN-ONE SCRIPT** ⭐

This is the **comprehensive, single-file solution** that includes everything from the dedicated Apache script plus complete Laravel setup.

**Complete Features:**
- ✅ **Full Apache installation and startup verification**
- ✅ **PHP installation with all required extensions**
- ✅ **MySQL server installation and configuration**
- ✅ **<PERSON><PERSON>-optimized Apache virtual host configuration**
- ✅ **Security headers and SSL template setup**
- ✅ **Apache modules enabling (rewrite, ssl, headers, expires)**
- ✅ **Configuration backup and testing**
- ✅ **GitHub repository cloning**
- ✅ **Laravel environment setup (.env configuration)**
- ✅ **Database creation and user setup**
- ✅ **Composer dependency installation**
- ✅ **Database migrations and Laravel optimization**
- ✅ **Proper file permissions and security**
- ✅ **Comprehensive service verification and status reporting**

### 2. `configure-laravel-ec2.sh` - Alternative Setup Script

Alternative version of the main setup script with advanced features.

### 3. `update-laravel-project.sh` - Project Update Script

This script updates your existing Laravel project with the latest changes from GitHub.

**Features:**
- ✅ Backup current configuration
- ✅ Pull latest changes from GitHub
- ✅ Update dependencies
- ✅ Run database migrations
- ✅ Clear and rebuild caches
- ✅ Set proper permissions

## Usage Instructions

### Initial Setup

1. **Upload scripts to your EC2 instance:**
   ```bash
   # Using SCP
   scp setup-laravel.sh ubuntu@your-ec2-ip:~/
   scp update-laravel-project.sh ubuntu@your-ec2-ip:~/

   # Or clone this repository
   git clone <repository-url>
   cd <repository-name>
   ```

2. **Make scripts executable:**
   ```bash
   chmod +x setup-laravel.sh
   chmod +x update-laravel-project.sh
   ```

3. **Run the complete setup script:**
   ```bash
   sudo ./setup-laravel.sh
   ```

4. **Follow the interactive prompts:**
   - Enter database name (default: laravel_db)
   - Enter database username (default: laravel_user)
   - Enter database password
   - Complete MySQL secure installation when prompted

### Updating Your Project

To update your Laravel project with the latest changes from GitHub:

```bash
sudo ./update-laravel-project.sh
```

## Configuration

### GitHub Repository

By default, the script clones from:
```
https://github.com/SocheatSorng/LaravelReact.git
```

To use a different repository, edit the `GITHUB_REPO` variable in `configure-laravel-ec2.sh`:

```bash
GITHUB_REPO="https://github.com/yourusername/yourproject.git"
PROJECT_NAME="YourProjectName"
```

### Directory Structure

The script sets up the following directory structure:
```
/var/www/html/
├── LaravelReact/
│   ├── backend/          # Laravel application
│   │   ├── public/       # Web root
│   │   ├── storage/
│   │   ├── .env
│   │   └── ...
│   └── ...
```

### Apache Configuration

The script configures Apache to serve the Laravel application from:
- **Document Root:** `/var/www/html/LaravelReact/backend/public`
- **Virtual Host:** Default (port 80)

## Security Features

- ✅ Firewall configuration (UFW)
- ✅ Proper file permissions (www-data:www-data)
- ✅ Secure MySQL installation
- ✅ Laravel security best practices

## Troubleshooting

### Common Issues

1. **Permission Denied Errors:**
   ```bash
   sudo chown -R www-data:www-data /var/www/html/LaravelReact
   sudo chmod -R 755 /var/www/html/LaravelReact
   sudo chmod -R 775 /var/www/html/LaravelReact/backend/storage
   ```

2. **Database Connection Issues:**
   - Check `.env` file configuration
   - Verify MySQL service is running: `sudo systemctl status mysql`
   - Test database connection: `mysql -u username -p database_name`

3. **Apache Issues:**
   - Check Apache status: `sudo systemctl status apache2`
   - View error logs: `sudo tail -f /var/log/apache2/error.log`
   - Restart Apache: `sudo systemctl restart apache2`

4. **Laravel Issues:**
   - View Laravel logs: `tail -f /var/www/html/LaravelReact/backend/storage/logs/laravel.log`
   - Clear caches: `php artisan cache:clear && php artisan config:clear`

### Log Files

- **Apache Error Log:** `/var/log/apache2/error.log`
- **Apache Access Log:** `/var/log/apache2/access.log`
- **Laravel Log:** `/var/www/html/LaravelReact/backend/storage/logs/laravel.log`

## Manual Commands

### Update from GitHub
```bash
cd /var/www/html/LaravelReact
git pull origin main
```

### Laravel Artisan Commands
```bash
cd /var/www/html/LaravelReact/backend
php artisan migrate
php artisan cache:clear
php artisan config:cache
```

### Service Management
```bash
sudo systemctl restart apache2
sudo systemctl restart mysql
sudo systemctl status apache2
sudo systemctl status mysql
```

## Requirements

- Ubuntu 20.04+ or similar Debian-based system
- Root or sudo access
- Internet connection for package downloads
- GitHub repository access

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review log files for error details
3. Ensure all requirements are met
4. Verify network connectivity and permissions

---

**Note:** Always backup your data before running these scripts in production environments.
