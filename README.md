# Laravel EC2 Setup Scripts

This repository contains optimized scripts for setting up and managing Laravel applications on Amazon EC2 instances.

## Scripts Overview

### 1. `setup-laravel.sh` - **RECOMMENDED** Main Setup Script

This is the **primary and most reliable** script that automates the complete setup of a Laravel application on EC2 by cloning from GitHub.

**Features:**
- ✅ **Explicit Apache installation and configuration**
- ✅ **Separate PHP installation with all required extensions**
- ✅ **MySQL server installation and setup**
- ✅ System package updates and installation
- ✅ Composer installation
- ✅ GitHub repository cloning
- ✅ Laravel environment configuration
- ✅ Database migrations and optimization
- ✅ Proper file permissions and security
- ✅ Service verification and status checks

### 2. `install-apache.sh` - Dedicated Apache Installation Script

A standalone script focused specifically on Apache installation and configuration for Laravel.

**Features:**
- ✅ **Complete Apache installation**
- ✅ **PHP module installation and configuration**
- ✅ **Laravel-optimized virtual host setup**
- ✅ **Security headers and configurations**
- ✅ **SSL template configuration**
- ✅ **Apache modules enabling (rewrite, ssl, headers, expires)**
- ✅ **Configuration backup and testing**

### 3. `configure-laravel-ec2.sh` - Alternative Setup Script

Alternative version of the main setup script with advanced features.

### 4. `update-laravel-project.sh` - Project Update Script

This script updates your existing Laravel project with the latest changes from GitHub.

**Features:**
- ✅ Backup current configuration
- ✅ Pull latest changes from GitHub
- ✅ Update dependencies
- ✅ Run database migrations
- ✅ Clear and rebuild caches
- ✅ Set proper permissions

## Usage Instructions

### Initial Setup

1. **Upload scripts to your EC2 instance:**
   ```bash
   # Using SCP
   scp setup-laravel.sh ubuntu@your-ec2-ip:~/
   scp install-apache.sh ubuntu@your-ec2-ip:~/
   scp update-laravel-project.sh ubuntu@your-ec2-ip:~/

   # Or clone this repository
   git clone <repository-url>
   cd <repository-name>
   ```

2. **Make scripts executable:**
   ```bash
   chmod +x setup-laravel.sh
   chmod +x install-apache.sh
   chmod +x update-laravel-project.sh
   ```

3. **Option A: Complete Setup (RECOMMENDED)**
   ```bash
   sudo ./setup-laravel.sh
   ```

4. **Option B: Step-by-step Setup**
   ```bash
   # First install Apache
   sudo ./install-apache.sh

   # Then run the main setup (will skip Apache installation)
   sudo ./setup-laravel.sh
   ```

5. **Follow the interactive prompts:**
   - Enter database name (default: laravel_db)
   - Enter database username (default: laravel_user)
   - Enter database password
   - Complete MySQL secure installation when prompted

### Updating Your Project

To update your Laravel project with the latest changes from GitHub:

```bash
sudo ./update-laravel-project.sh
```

## Configuration

### GitHub Repository

By default, the script clones from:
```
https://github.com/SocheatSorng/LaravelReact.git
```

To use a different repository, edit the `GITHUB_REPO` variable in `configure-laravel-ec2.sh`:

```bash
GITHUB_REPO="https://github.com/yourusername/yourproject.git"
PROJECT_NAME="YourProjectName"
```

### Directory Structure

The script sets up the following directory structure:
```
/var/www/html/
├── LaravelReact/
│   ├── backend/          # Laravel application
│   │   ├── public/       # Web root
│   │   ├── storage/
│   │   ├── .env
│   │   └── ...
│   └── ...
```

### Apache Configuration

The script configures Apache to serve the Laravel application from:
- **Document Root:** `/var/www/html/LaravelReact/backend/public`
- **Virtual Host:** Default (port 80)

## Security Features

- ✅ Firewall configuration (UFW)
- ✅ Proper file permissions (www-data:www-data)
- ✅ Secure MySQL installation
- ✅ Laravel security best practices

## Troubleshooting

### Common Issues

1. **Permission Denied Errors:**
   ```bash
   sudo chown -R www-data:www-data /var/www/html/LaravelReact
   sudo chmod -R 755 /var/www/html/LaravelReact
   sudo chmod -R 775 /var/www/html/LaravelReact/backend/storage
   ```

2. **Database Connection Issues:**
   - Check `.env` file configuration
   - Verify MySQL service is running: `sudo systemctl status mysql`
   - Test database connection: `mysql -u username -p database_name`

3. **Apache Issues:**
   - Check Apache status: `sudo systemctl status apache2`
   - View error logs: `sudo tail -f /var/log/apache2/error.log`
   - Restart Apache: `sudo systemctl restart apache2`

4. **Laravel Issues:**
   - View Laravel logs: `tail -f /var/www/html/LaravelReact/backend/storage/logs/laravel.log`
   - Clear caches: `php artisan cache:clear && php artisan config:clear`

### Log Files

- **Apache Error Log:** `/var/log/apache2/error.log`
- **Apache Access Log:** `/var/log/apache2/access.log`
- **Laravel Log:** `/var/www/html/LaravelReact/backend/storage/logs/laravel.log`

## Manual Commands

### Update from GitHub
```bash
cd /var/www/html/LaravelReact
git pull origin main
```

### Laravel Artisan Commands
```bash
cd /var/www/html/LaravelReact/backend
php artisan migrate
php artisan cache:clear
php artisan config:cache
```

### Service Management
```bash
sudo systemctl restart apache2
sudo systemctl restart mysql
sudo systemctl status apache2
sudo systemctl status mysql
```

## Requirements

- Ubuntu 20.04+ or similar Debian-based system
- Root or sudo access
- Internet connection for package downloads
- GitHub repository access

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Review log files for error details
3. Ensure all requirements are met
4. Verify network connectivity and permissions

---

**Note:** Always backup your data before running these scripts in production environments.
